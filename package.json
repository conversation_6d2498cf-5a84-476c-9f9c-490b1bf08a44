{"name": "f0ck_beta", "version": "3.5.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "prod": "npm run build && npm run start", "docker:prod": "docker compose up -d"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@types/socket.io-client": "^1.4.36", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "file-type": "^20.4.0", "install": "^0.13.0", "isomorphic-dompurify": "^2.22.0", "lucide-react": "^0.483.0", "mongodb": "^6.15.0", "mongoose": "^8.12.2", "next": "^15.3.1", "next-auth": "^4.24.11", "npm": "^11.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "stripe": "^18.1.0", "styled-components": "^6.1.16", "tailwind-merge": "^3.0.2", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/forms": "^0.5.10", "@types/bcryptjs": "^3.0.0", "@types/mongoose": "^5.11.97", "@types/node": "^22.15.3", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}